import { useDesignSpace } from "@contexts/DesignSpaceContext";
import { isEmpty } from "lodash";
import { useEffect, useRef } from "react";

export default function Element({ el, scaleFactor = 1, userData = {}, selectedIds = [] }) {

    // Function to replace variables with user data
    const replaceVariables = (text) => {
        if (!text || typeof text !== 'string') return text;
        
        return text.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
            // Handle different variable formats
            const cleanVariable = variable.replace(/^\$/, ''); // Remove $ prefix if exists
            const replacement = userData[cleanVariable];
            
            // If we found a replacement value, return it without any brackets
            if (replacement !== undefined && replacement !== null) {
                return replacement;
            }
            
            // If no replacement found, return the variable name without brackets
            return cleanVariable;
        });
    };

    // Check if element contains variables (either has user-data class or contains {{}})
    const hasVariables = el.className === "user-data" || 
                        (el.value && typeof el.value === 'string' && el.value.includes('{{'));

    if (hasVariables) {
        return <UserDataElement el={el} scaleFactor={scaleFactor} userData={userData} />
    }

    switch (el.type) {
        case "img":
            // If it's an avatar and we have user data, use the user's image
            if (!isEmpty(userData) && el.value === "avatar") {
                el = { ...el, value: userData?.image || "https://www.gravatar.com/avatar/?d=mp" };
            }
            return <Image el={el} />

        case "qr":
            return <QrImage el={el} />

        case "icon":
            return <Icon el={el} />

        case "text":
            // Handle dynamic fields for text elements
            if (el.isDynamicField && !isEmpty(userData)) {
                // Replace the field value with actual user data
                const fieldValue = userData[el.fieldKey] || el.value;
                el = { ...el, value: fieldValue };
            } else if (!isEmpty(userData) && el.value) {
                // For regular text elements, try to replace variables
                el = { ...el, value: userData?.[el.value] || el.value };
            }
            
            // Always replace any remaining {{}} variables in the text
            if (el.value && typeof el.value === 'string' && el.value.includes('{{')) {
                el = { ...el, value: replaceVariables(el.value) };
            }
            
            return <Text el={el} scaleFactor={scaleFactor} selectedIds={selectedIds} />

        case "label":
            return <Label el={el} scaleFactor={scaleFactor} />

        case "shape":
            return <Shape el={el} />

        case "line":
            return <Line el={el} />

        case "frame":
            return <Frame el={el} />

        case "sticker":
            return <Sticker el={el} />

        case "gradient":
            return <Gradient el={el} />

        case "svg":
            return <SVGElement el={el} />

        default:
            break;
    }
}

const Image = ({ el }) => {
    // objectPosX, objectPosY: نسبة مئوية (0-100)
    const objectPosX = el.objectPosX || 0;
    const objectPosY = el.objectPosY || 0;

    return (
        <img
            loading="lazy"
            src={el.value}
            alt="holder"
            draggable={false}
            style={{
                borderRadius: el.borderRadius,
                width: "100%",
                height: "100%",
                objectFit: 'cover',
                objectPosition: `${objectPosX}% ${objectPosY}%`,
                ...(el.style || {})
            }}
        />
    )
}

const QrImage = ({ el }) => {
    return (
        <img
            loading="lazy"
            src={el.value}
            alt="Qr Code"
            style={{
                width: "100%",
                height: "100%",
            }}
        />
    )
}


const Label = ({ el, scaleFactor }) => {
    const { updateElement } = useDesignSpace();

    // Function to clean brackets for editing
    const cleanValueForEditing = (value) => {
        if (!value || typeof value !== 'string') return value;
        // Remove {{}} brackets for editing
        return value.replace(/\{\{(\w+)\}\}/g, '$1');
    };

    // Function to restore brackets when saving
    const restoreBrackets = (value) => {
        if (!value || typeof value !== 'string') return value;
        // If it's a field name without brackets, add them back
        const fieldNames = ['name', 'type', 'position', 'department', 'custom_field_1', 'custom_field_2', 'custom_field_3', 'custom_field_4', 'custom_field_5', 'custom_field_6', 'custom_field_7', 'custom_field_8', 'custom_field_9', 'custom_field_10'];
        if (fieldNames.includes(value)) {
            return `{{${value}}}`;
        }
        return value;
    };

    return (
        <input
            type="text"
            value={cleanValueForEditing(el.value) || "Enter your text here"}
            placeholder="Enter your text here"
            onChange={(e) => updateElement(el.id, { value: restoreBrackets(e.target.value) })}
            style={{
                textDecoration: el.isUnderlined ? "underline" : "none",
                backgroundColor: "transparent",
                fontWeight: el.isBold ? "bold" : "normal",
                fontFamily: el.fontFamily,
                fontStyle: el.isItalic ? "italic" : "normal",
                textTransform: el.textTransform,
                textAlign: el.textAlign,
                fontSize: el.fontSize * scaleFactor,
                overflow: "visible",
                padding: "2px",
                color: el.color,
                height: "100%",
                width: "100%",
            }} />
    )
}

const Text = ({ el, scaleFactor = 1, selectedIds = [] }) => {
    const { updateElement } = useDesignSpace();
    const ref = useRef();
    // يعتبر النص ديناميكي إذا كان يحتوي على {{}} أو خاصية isDynamicField
    const isDynamic = el.isDynamicField || (typeof el.value === 'string' && el.value.includes('{{'));
    const isSelected = selectedIds && selectedIds.includes(el.id);

    // دالة لكشف إذا كان النص عربيًا
    const isArabic = (text) => /[\u0600-\u06FF]/.test(text);
    const currentValue = el.value || '';
    const rtl = isArabic(currentValue);

    // مزامنة النص مع contentEditable عند التحديد
    useEffect(() => {
        if (isSelected && ref.current && !isDynamic) {
            ref.current.innerText = el.value || '';
        }
    }, [isSelected, isDynamic, el.id]);

    // عند التعديل على النص مباشرة
    const handleInput = (e) => {
        // لا تحدث state هنا
    };

    const handleBlur = (e) => {
        if (!isDynamic) {
            const newText = e.currentTarget.innerText;
            updateElement(el.id, { value: newText });
            window.dispatchEvent(new CustomEvent('designTextInput', { detail: { id: el.id, value: newText } }));
        }
    };

    return (
        <div
            ref={ref}
            contentEditable={isSelected && !isDynamic}
            suppressContentEditableWarning
            spellCheck={false}
            onInput={handleInput}
            onBlur={handleBlur}
            style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                textAlign: rtl ? "right" : (el.textAlign || "center"),
                direction: rtl ? "rtl" : "ltr",
                fontSize: (el.fontSize || 16) * scaleFactor,
                fontFamily: el.fontFamily || "Arial, sans-serif",
                fontWeight: el.fontWeight || "normal",
                fontStyle: el.fontStyle || "normal",
                color: el.color || "#000000",
                backgroundColor: el.backgroundColor || "transparent",
                background: el.background || el.backgroundColor || "transparent",
                textDecoration: el.textDecoration || "none",
                textTransform: el.textTransform || "none",
                lineHeight: el.lineHeight || 1.2,
                letterSpacing: el.letterSpacing || "normal",
                whiteSpace: "pre-wrap",
                wordWrap: "break-word",
                overflow: "hidden",
                padding: "4px",
                borderRadius: el.borderRadius || 0,
                border: el.border || "none",
                boxShadow: el.boxShadow || "none",
                opacity: el.opacity || 1,
                transform: el.transform || "none",
                userSelect: isSelected && !isDynamic ? "text" : "none",
                outline: isSelected && !isDynamic ? "2px solid #3b82f6" : "none",
                cursor: isSelected && !isDynamic ? "text" : "move",
            }}
            tabIndex={isSelected && !isDynamic ? 0 : -1}
        >
            {currentValue || "Text"}
        </div>
    );
};

const UserDataElement = ({ el, scaleFactor, userData }) => {
    // Function to replace variables with user data
    const replaceVariables = (text) => {
        if (!text || typeof text !== 'string') return text;
        
        return text.replace(/\{\{(\w+)\}\}/g, (match, variable) => {
            // Handle different variable formats
            const cleanVariable = variable.replace(/^\$/, ''); // Remove $ prefix if exists
            const replacement = userData[cleanVariable];
            
            // If we found a replacement value, return it without any brackets
            if (replacement !== undefined && replacement !== null) {
                return replacement;
            }
            
            // If no replacement found, return the variable name without brackets
            return cleanVariable;
        });
    };

    // Get the content from the element
    let content = el.value || el.textContent || el.innerHTML || "Your text content here";
    
    // Replace variables if userData is provided
    if (!isEmpty(userData)) {
        content = replaceVariables(content);
    }

    return (
        <div
            className="user-data"
            style={{
                textDecoration: el.isUnderlined ? "underline" : "none",
                backgroundColor: "transparent",
                fontWeight: el.isBold ? "bold" : "normal",
                fontFamily: el.fontFamily || "Roboto",
                fontStyle: el.isItalic ? "italic" : "normal",
                textTransform: el.textTransform || "lowercase",
                textAlign: el.textAlign || "center",
                fontSize: el.fontSize || "16px",
                whiteSpace: "normal",
                wordWrap: "break-word",
                overflow: "visible",
                padding: "2px",
                color: el.color || "black",
                height: "100%",
                width: "100%",
            }}
        >
            {content}
        </div>
    )
}

const Icon = ({ el }) => {
    return (
        <img
            loading="lazy"
            src={el.value}
            style={{
                width: "100%",
                height: "100%",
            }}
        />
    )
}

// Shape component for rendering different shapes
const Shape = ({ el }) => {
    const shapeStyles = {
        width: "100%",
        height: "100%",
        backgroundColor: el.backgroundColor || "#4338ca",
        background: el.background || el.backgroundColor || "#4338ca",
        userSelect: "none",
    };

    switch (el.shapeType) {
        case "rectangle":
            return <div style={{ ...shapeStyles, borderRadius: "4px" }}></div>;
        case "square":
            return <div style={{ ...shapeStyles, borderRadius: "4px" }}></div>;
        case "circle":
            return <div style={{ ...shapeStyles, borderRadius: "50%" }}></div>;
        case "triangle":
            return (
                <div
                    style={{
                        width: "0",
                        height: "0",
                        borderLeft: `${el.width / 2}px solid transparent`,
                        borderRight: `${el.width / 2}px solid transparent`,
                        borderBottom: `${el.height}px solid ${el.backgroundColor || el.background || "#4338ca"}`,
                    }}
                ></div>
            );
        case "star":
            return (
                <div
                    style={{
                        ...shapeStyles,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: Math.min(el.width, el.height) * 0.8,
                        color: el.backgroundColor || el.background || "#4338ca",
                        backgroundColor: "transparent",
                    }}
                >
                    ★
                </div>
            );
        case "heart":
            return (
                <div
                    style={{
                        ...shapeStyles,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: Math.min(el.width, el.height) * 0.8,
                        color: el.backgroundColor || el.background || "#4338ca",
                        backgroundColor: "transparent",
                    }}
                >
                    ❤
                </div>
            );
        case "pentagon":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="50,0 100,38 81,100 19,100 0,38" />
                </svg>
            );
        case "hexagon":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="25,0 75,0 100,50 75,100 25,100 0,50" />
                </svg>
            );
        case "diamond":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="50,0 100,50 50,100 0,50" />
                </svg>
            );
        case "rounded-square":
            return <div style={{ ...shapeStyles, borderRadius: "15px" }}></div>;
        case "oval":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <ellipse cx="50" cy="50" rx="50" ry="35" />
                </svg>
            );
        case "trapezoid":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="20,0 80,0 100,100 0,100" />
                </svg>
            );
        case "parallelogram":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="25,0 100,0 75,100 0,100" />
                </svg>
            );
        case "octagon":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 100" style={{ fill: el.backgroundColor || el.background || "#4338ca" }}>
                    <polygon points="30,0 70,0 100,30 100,70 70,100 30,100 0,70 0,30" />
                </svg>
            );
        default:
            return <div style={shapeStyles}></div>;
    }
};

// Line component for rendering different line types
const Line = ({ el }) => {
    const lineColor = el.strokeColor || el.background || "#4338ca";
    const strokeWidth = el.strokeWidth || 2;

    switch (el.lineType) {
        case "straight":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: lineColor, strokeWidth: strokeWidth }}>
                    <line x1="0" y1="5" x2="100" y2="5" />
                </svg>
            );
        case "dashed":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: lineColor, strokeWidth: strokeWidth }}>
                    <line x1="0" y1="5" x2="100" y2="5" strokeDasharray="5,5" />
                </svg>
            );
        case "dotted":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: lineColor, strokeWidth: strokeWidth }}>
                    <line x1="0" y1="5" x2="100" y2="5" strokeDasharray="2,2" />
                </svg>
            );
        case "double":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: lineColor, strokeWidth: strokeWidth }}>
                    <line x1="0" y1="3" x2="100" y2="3" />
                    <line x1="0" y1="7" x2="100" y2="7" />
                </svg>
            );
        case "wavy":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: lineColor, strokeWidth: strokeWidth, fill: "none" }}>
                    <path d="M0,10 Q25,0 50,10 T100,10" />
                </svg>
            );
        case "zigzag":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: lineColor, strokeWidth: strokeWidth, fill: "none" }}>
                    <path d="M0,10 L25,0 L50,10 L75,0 L100,10" />
                </svg>
            );
        case "curved":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: lineColor, strokeWidth: strokeWidth, fill: "none" }}>
                    <path d="M0,10 Q50,0 100,10" />
                </svg>
            );
        case "arrow":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: lineColor, strokeWidth: strokeWidth, fill: "none" }}>
                    <line x1="0" y1="10" x2="80" y2="10" />
                    <polygon points="80,5 100,10 80,15" fill={lineColor} />
                </svg>
            );
        case "double-arrow":
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 20" style={{ stroke: lineColor, strokeWidth: strokeWidth, fill: "none" }}>
                    <polygon points="20,5 0,10 20,15" fill={lineColor} />
                    <line x1="20" y1="10" x2="80" y2="10" />
                    <polygon points="80,5 100,10 80,15" fill={lineColor} />
                </svg>
            );
        default:
            return (
                <svg width="100%" height="100%" viewBox="0 0 100 10" style={{ stroke: lineColor, strokeWidth: strokeWidth }}>
                    <line x1="0" y1="5" x2="100" y2="5" />
                </svg>
            );
    }
};

// Frame component for rendering different frame types
const Frame = ({ el }) => {
    const frameColor = el.borderColor || el.background || "#4338ca";
    const borderWidth = el.borderWidth || 2;
    
    const frameStyles = {
        width: "100%",
        height: "100%",
        border: `${borderWidth}px solid ${frameColor}`,
        backgroundColor: "transparent",
        userSelect: "none",
    };

    switch (el.frameType) {
        case "rectangle":
            return <div style={{ ...frameStyles, borderRadius: "4px" }}></div>;
        case "square":
            return <div style={{ ...frameStyles, borderRadius: "4px" }}></div>;
        case "circle":
            return <div style={{ ...frameStyles, borderRadius: "50%" }}></div>;
        case "heart":
            return (
                <div
                    style={{
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        color: frameColor,
                        fontSize: Math.min(el.width, el.height) * 0.8,
                    }}
                >
                    ♡
                </div>
            );
        default:
            return <div style={frameStyles}></div>;
    }
};

// Sticker component for rendering different sticker types
const Sticker = ({ el }) => {
    // Emoji mapping for stickers
    const emojiMap = {
        // Emoji stickers
        "smile": "😊",
        "laugh": "😂",
        "love": "😍",
        "cool": "😎",
        "wink": "😉",
        "think": "🤔",
        "wow": "😮",
        "sad": "😢",
        "angry": "😡",

        // Symbol stickers
        "star": "⭐",
        "heart": "❤️",
        "fire": "🔥",
        "check": "✅",
        "cross": "❌",
        "warning": "⚠️",
        "lightning": "⚡",
        "music": "🎵",
        "trophy": "🏆",

        // Object stickers
        "gift": "🎁",
        "balloon": "🎈",
        "camera": "📷",
        "phone": "📱",
        "computer": "💻",
        "bulb": "💡",
        "money": "💰",
        "rocket": "🚀",
        "clock": "🕒",

        // Default for backward compatibility
        "arrow": "➡️",
        "shapes": "🔶"
    };

    return (
        <div
            style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontSize: Math.min(el.width, el.height) * 0.6,
                userSelect: "none",
            }}
        >
            {emojiMap[el.stickerType] || "🔹"}
        </div>
    );
};

// Gradient component for rendering gradient backgrounds
const Gradient = ({ el }) => {
    return (
        <div
            style={{
                width: "100%",
                height: "100%",
                background: el.style?.background || "linear-gradient(45deg, #1e3c72, #2a5298)",
                borderRadius: "8px",
                userSelect: "none",
            }}
        ></div>
    );
};

// SVG component for rendering complex SVG elements
const SVGElement = ({ el }) => {
    // Check if the SVG content is available
    if (!el.value) {
        return (
            <div
                style={{
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    backgroundColor: "#f0f0f0",
                    color: "#666",
                    border: "1px dashed #999",
                    borderRadius: "4px",
                    padding: "8px",
                    fontSize: "12px",
                    textAlign: "center"
                }}
            >
                SVG content not available
            </div>
        );
    }

    // For security, we'll use dangerouslySetInnerHTML but only for SVG content
    // This is generally not recommended for user-generated content without proper sanitization
    return (
        <div
            style={{
                width: "100%",
                height: "100%",
                display: "flex",
                alignItems: "center",
                justifyContent: "center"
            }}
            dangerouslySetInnerHTML={{ __html: el.value }}
        />
    );
};