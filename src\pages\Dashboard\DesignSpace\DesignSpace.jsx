import { useState, useEffect, useRef } from "react";
import PropTypes from 'prop-types';

import { useDesignSpace } from "@contexts/DesignSpaceContext";
import "./DesignSpaceOverrides.css"; // Import custom CSS to override transparency
import "./RotationHandles.css"; // Import custom CSS for rotation handles
import "./cursors.css"; // Import professional cursors
import "./MobileDesignSpace.css"; // Import mobile-specific styles

import AlignmentContainer from "./components/AlignmentContainer";
import AlignmentControl from "./components/AlignmentControl";
import DuplicateControl from "./components/DuplicateControl";
import DeleteControl from "./components/DeleteControl";
import ResizeInputs from "./components/ResizeInputs";
import TypeControl from "./components/TypeControl";
import Element from "./components/Element";
import CanvaToolbar from "./components/CanvaToolbar";
import DesignSpaceBackground from "./components/DesignSpaceBackground";
import LeftSidebar from "./components/LeftSidebar";
import { FiHelpCircle, FiChevronLeft } from 'react-icons/fi';
import { motion } from 'framer-motion';
import { MdOutlineColorLens } from 'react-icons/md';
import { FaRegStar } from 'react-icons/fa';
import { useGlobalContext } from '@contexts/GlobalContext';
import ColorPicker from "./components/ColorPicker";

const DesignSpace = ({ updateTemplateData, design }) => {
    const [alignmentLines, setAlignmentLines] = useState({ vertical: null, horizontal: null });
    const [toolbarPosition, setToolbarPosition] = useState(null);
    const [toolbarClasses, setToolbarClasses] = useState('');

    // Mobile responsiveness states
    const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

    const {
        selectedIds, setSelectedIds,
        updateElement, designSpaceRef,
        elements, setElements, isMultiSelectActive,
        setSelectedElement, cardType,
        canvasBackgroundStyle,
        bringToFront, sendToBack,
        zoomLevel,
        saveTextStyle
    } = useDesignSpace();

    const clipboardRef = useRef(null);

    const { dialogHandler } = useGlobalContext();

    const [pendingSave, setPendingSave] = useState(false);

    const [draggingElementId, setDraggingElementId] = useState(null);
    // refs for drag threshold
    const dragStartX = useRef(0);
    const dragStartY = useRef(0);
    const isActuallyDragging = useRef(false);

    const [colorPickerTargetId, setColorPickerTargetId] = useState(null);

    // بعد useState الأخرى
    const [activeCrop, setActiveCrop] = useState({ elementId: null, side: null });
    const [activeResize, setActiveResize] = useState({ elementId: null, corner: null });

    const [initialElements, setInitialElements] = useState([]);
    const [initialBackgroundStyle, setInitialBackgroundStyle] = useState(null);
    const [isDirty, setIsDirty] = useState(false);

    // Mobile detection useEffect
    useEffect(() => {
        const handleResize = () => {
            const mobileView = window.innerWidth < 768;
            setIsMobile(mobileView);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Mobile touch gesture support
    useEffect(() => {
        if (!isMobile) return;

        let touchStartX = 0;
        let touchStartY = 0;
        let touchStartTime = 0;

        const handleTouchStart = (e) => {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            touchStartTime = Date.now();
        };

        const handleTouchEnd = (e) => {
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            const touchEndTime = Date.now();

            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            const deltaTime = touchEndTime - touchStartTime;

            // Detect swipe gestures
            if (Math.abs(deltaX) > 50 && deltaTime < 300) {
                if (deltaX > 0) {
                    // Swipe right - could be used for navigation
                    console.log('Swipe right detected');
                } else {
                    // Swipe left - could be used for navigation
                    console.log('Swipe left detected');
                }
            }

            if (Math.abs(deltaY) > 50 && deltaTime < 300) {
                if (deltaY > 0) {
                    // Swipe down - could close mobile panels
                    console.log('Swipe down detected');
                } else {
                    // Swipe up - could open mobile panels
                    console.log('Swipe up detected');
                }
            }
        };

        const designSpace = designSpaceRef.current;
        if (designSpace) {
            designSpace.addEventListener('touchstart', handleTouchStart, { passive: true });
            designSpace.addEventListener('touchend', handleTouchEnd, { passive: true });

            return () => {
                designSpace.removeEventListener('touchstart', handleTouchStart);
                designSpace.removeEventListener('touchend', handleTouchEnd);
            };
        }
    }, [isMobile, designSpaceRef]);

    // Helper function to get mouse position relative to design space, considering zoom
    const getRelativeMousePosition = (e, designSpaceRect, zoomLevel) => {
        // zoomLevel is a percentage (e.g., 100, 120, 80)
        const scale = zoomLevel / 100;
        const x = (e.clientX - designSpaceRect.left) / scale;
        const y = (e.clientY - designSpaceRect.top) / scale;
        return { x, y };
    };

    // دالة مساعدة لحساب موضع شريط الأدوات
    const updateToolbarForElement = (element) => {
        // Get the design space container
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        // Get the DOM element
        const domElement = document.querySelector(`[data-element-id="${element.id}"]`);
        if (!domElement) return;
        const elementRect = domElement.getBoundingClientRect();
        const elementTop = elementRect.top - designSpaceRect.top;
        const elementRight = designSpaceRect.right - elementRect.right;
        const elementLeft = elementRect.left - designSpaceRect.left;
        const elementWidth = elementRect.width;
        const elementHeight = elementRect.height;
        const edgeThreshold = 50;
        const toolbarDistance = 60;
        const isNearTop = elementTop < edgeThreshold;
        const isNearRight = elementRight < edgeThreshold;
        const isNearLeft = elementLeft < edgeThreshold;
        let position = {
            position: 'absolute',
            display: 'flex',
            zIndex: 1000,
            pointerEvents: 'auto'
        };
        if (isNearRight) {
            position = {
                ...position,
                left: `-${toolbarDistance}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else if (isNearLeft) {
            position = {
                ...position,
                left: `${elementWidth + 20}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else {
            position = {
                ...position,
                top: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
        if (isNearTop && !isNearRight && !isNearLeft) {
            position = {
                ...position,
                top: 'auto',
                bottom: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
        setToolbarPosition(position);
        let classes = '';
        if (isNearTop) classes += ' top-edge';
        if (isNearRight) classes += ' right-edge';
        if (isNearLeft) classes += ' left-edge';
        setToolbarClasses(classes.trim());
    };

    const handleMouseDown = (e, id, resizeCorner = null) => {
        e.stopPropagation();
        // Get design space dimensions
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        // Get initial mouse position (relative to design space, considering zoom)
        const { x: startX, y: startY } = getRelativeMousePosition(e, designSpaceRect, zoomLevel);
        dragStartX.current = e.clientX;
        dragStartY.current = e.clientY;
        isActuallyDragging.current = false;
        let rafId = null;
        let lastDeltaX = 0;
        let lastDeltaY = 0;
        let isResizing = resizeCorner !== null;

        // Get the element from the elements array
        const elementIndex = elements.findIndex(el => el.id === id);
        if (elementIndex === -1) return;
        const element = {...elements[elementIndex]};
        const initialX = element.x;
        const initialY = element.y;
        const initialWidth = element.width;
        const initialHeight = element.height;

        // لا يوجد منطق خاص للصور بعد الآن

        const updateElementPosition = () => {
            // Create a copy of the elements array
            const newElements = [...elements];

            // Handle resizing
            if (isResizing) {
                let newWidth = initialWidth;
                let newHeight = initialHeight;
                let newX = initialX;
                let newY = initialY;

                // إذا كان العنصر صورة، غير الأبعاد بشكل متساوي فقط
                if (element.type === 'img') {
                    // احسب التغير في المسافة بناءً على الزاوية
                    let delta = 0;
                    if (resizeCorner === 'top-left' || resizeCorner === 'bottom-right') {
                        // استخدم المتوسط الحسابي للتغيرات
                        delta = (lastDeltaX + lastDeltaY) / 2;
                    } else if (resizeCorner === 'top-right' || resizeCorner === 'bottom-left') {
                        delta = (lastDeltaY - lastDeltaX) / 2;
                    }
                    // احسب النسبة الأصلية
                    const aspectRatio = initialWidth / initialHeight;
                    // احسب الأبعاد الجديدة مع الحفاظ على النسبة
                    let size = Math.max(20, Math.min(initialWidth + delta, initialHeight + delta * (1/aspectRatio)));
                    newWidth = size;
                    newHeight = size / aspectRatio;
                    // عدل الموضع إذا كان السحب من الأعلى أو اليسار
                    if (resizeCorner.includes('left')) {
                        newX = initialX + (initialWidth - newWidth);
                    }
                    if (resizeCorner.includes('top')) {
                        newY = initialY + (initialHeight - newHeight);
                    }
                } else {
                    // تغيير الحجم العادي للعناصر الأخرى
                    // Calculate new dimensions based on the corner being dragged
                    if (resizeCorner.includes("right")) {
                        newWidth = Math.max(20, initialWidth + lastDeltaX);
                    }
                    if (resizeCorner.includes("left")) {
                        const widthChange = lastDeltaX;
                        newWidth = Math.max(20, initialWidth - widthChange);
                        // Keep the right edge fixed
                        newX = initialX + widthChange;
                    }
                    if (resizeCorner.includes("bottom")) {
                        newHeight = Math.max(20, initialHeight + lastDeltaY);
                    }
                    if (resizeCorner.includes("top")) {
                        const heightChange = lastDeltaY;
                        newHeight = Math.max(20, initialHeight - heightChange);
                        // Keep the bottom edge fixed
                        newY = initialY + heightChange;
                    }
                }

                // Apply constraints to keep element within design space
                if (resizeCorner.includes("left")) {
                    newX = Math.max(0, Math.min(newX, designSpaceRect.width - newWidth));
                    if (newX === 0) {
                        newWidth = initialWidth + initialX;
                    } else if (newX === designSpaceRect.width - newWidth) {
                        newWidth = designSpaceRect.width - newX;
                    }
                }
                if (resizeCorner.includes("right")) {
                    newWidth = Math.min(newWidth, designSpaceRect.width - initialX);
                }
                if (resizeCorner.includes("top")) {
                    newY = Math.max(0, Math.min(newY, designSpaceRect.height - newHeight));
                    if (newY === 0) {
                        newHeight = initialHeight + initialY;
                    } else if (newY === designSpaceRect.height - newHeight) {
                        newHeight = designSpaceRect.height - newY;
                    }
                }
                if (resizeCorner.includes("bottom")) {
                    newHeight = Math.min(newHeight, designSpaceRect.height - initialY);
                }

                // Update the element in the array
                newElements[elementIndex] = {
                    ...element,
                    x: newX,
                    y: newY,
                    width: newWidth,
                    height: newHeight
                };

                // Update React state
                setElements(newElements);
            }
            // Handle moving only if not resizing
            else {
                // حماية إضافية: الصور لا تتحرك إلا إذا كان isActuallyDragging حقيقي
                if (element.type === 'img' && !isActuallyDragging.current) {
                    return;
                }
                // Calculate new position
                const newX = Math.max(0, Math.min(initialX + lastDeltaX, designSpaceRect.width - element.width));
                const newY = Math.max(0, Math.min(initialY + lastDeltaY, designSpaceRect.height - element.height));

                // التلاعب المباشر بموقع العنصر في DOM (يُفضل إزالته)
                // const domElement = document.querySelector(`[data-element-id="${element.id}"]`);
                // if (domElement) {
                //     domElement.style.left = `${newX}px`;
                //     domElement.style.top = `${newY}px`;
                //     // ... فقط تحديث toolbar إذا لزم الأمر ...
                // }

                // Update the element in the array
                newElements[elementIndex] = {
                    ...element,
                    x: newX,
                    y: newY
                };

                // Update React state
                setElements(newElements);

                // Show alignment lines
                showAlignmentLines(newX, newY, element, designSpaceRect);
            }

            rafId = null;
        };

        const handleMouseMove = (e) => {
            // إذا كان العنصر صورة ولم يتم تفعيل السحب الحقيقي، لا تفعل شيئًا
            if (element.type === 'img' && !isActuallyDragging.current) {
                const distance = Math.sqrt(Math.pow(e.clientX - dragStartX.current, 2) + Math.pow(e.clientY - dragStartY.current, 2));
                if (distance > 6) {
                    isActuallyDragging.current = true;
                    setDraggingElementId(id);
                } else {
                    return;
                }
            }
            // احسب الموضع الجديد للماوس مع مراعاة الزوم
            const { x: currentX, y: currentY } = getRelativeMousePosition(e, designSpaceRect, zoomLevel);
            lastDeltaX = currentX - startX;
            lastDeltaY = currentY - startY;

            if (!rafId) {
                rafId = requestAnimationFrame(updateElementPosition);
            }
        };

        const handleMouseUp = () => {
            setAlignmentLines({ vertical: null, horizontal: null });
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
            isResizing = false;
            setDraggingElementId(null);
            isActuallyDragging.current = false;
            // حماية إضافية: إذا كان العنصر صورة، أوقف أي حركة متبقية
            if (element.type === 'img') {
                lastDeltaX = 0;
                lastDeltaY = 0;
            }
            // بعد الإفلات، إذا كان العنصر ما زال محددًا، أعد حساب موضع شريط الأدوات
            setTimeout(() => {
                const dragged = elements[elementIndex];
                if (dragged && selectedIds.includes(dragged.id)) {
                    updateToolbarForElement(dragged);
                }
            }, 0);
            setActiveResize({ elementId: null, corner: null });
        };

        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
    };

    // Handle rotation of elements
    const handleRotationStart = (e, id) => {
        e.stopPropagation();
        if (!selectedIds.includes(id)) {
            return;
        }
        const element = elements.find(el => el.id === id);
        if (!element) return;
        const domElement = document.querySelector(`[data-element-id="${id}"]`);
        const elementRect = domElement.getBoundingClientRect();
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        const scale = zoomLevel / 100;
        // احسب مركز العنصر مع مراعاة الزوم
        const centerX = (elementRect.left - designSpaceRect.left) / scale + elementRect.width / (2 * scale);
        const centerY = (elementRect.top - designSpaceRect.top) / scale + elementRect.height / (2 * scale);
        // احسب زاوية البداية
        const { x: mouseX, y: mouseY } = getRelativeMousePosition(e, designSpaceRect, zoomLevel);
        const startAngle = Math.atan2(mouseY - centerY, mouseX - centerX) * (180 / Math.PI);
        const initialRotation = element.rotation || 0;
        let lastClientX = 0;
        let lastClientY = 0;
        let rotationRafId = null;
        let isRotating = true;
        const updateElementRotation = () => {
            if (!isRotating || !selectedIds.includes(id)) {
                isRotating = false;
                if (rotationRafId) {
                    cancelAnimationFrame(rotationRafId);
                    rotationRafId = null;
                }
                return;
            }
            // احسب الموضع الحالي للماوس مع مراعاة الزوم
            const { x: moveX, y: moveY } = getRelativeMousePosition({ clientX: lastClientX, clientY: lastClientY }, designSpaceRect, zoomLevel);
            const newAngle = Math.atan2(moveY - centerY, moveX - centerX) * (180 / Math.PI);
            let rotationDelta = newAngle - startAngle;
            const newRotation = initialRotation + rotationDelta;
            if (domElement && selectedIds.includes(id)) {
                domElement.style.transform = `rotate(${newRotation}deg)`;
                domElement.style.transformOrigin = 'center center';
                domElement.style.willChange = 'transform';
                domElement._pendingRotation = newRotation;
            }
            rotationRafId = null;
        };
        const handleRotationMove = (e) => {
            lastClientX = e.clientX;
            lastClientY = e.clientY;
            if (!rotationRafId) {
                rotationRafId = requestAnimationFrame(updateElementRotation);
            }
        };
        const handleRotationEnd = () => {
            isRotating = false;
            if (rotationRafId) {
                cancelAnimationFrame(rotationRafId);
                rotationRafId = null;
            }

            // Remove event listeners
            document.removeEventListener('mousemove', handleRotationMove);
            document.removeEventListener('mouseup', handleRotationEnd);

            // Apply pending rotation to React state only if element is still selected
            if (selectedIds.includes(id)) {
                const domElementFinal = document.querySelector(`[data-element-id="${id}"]`);
                if (domElementFinal && domElementFinal._pendingRotation !== undefined) {
                    updateElement(id, { rotation: domElementFinal._pendingRotation });
                    delete domElementFinal._pendingRotation;
                }
            }
        };

        // Add event listeners
        document.addEventListener('mousemove', handleRotationMove);
        document.addEventListener('mouseup', handleRotationEnd);

        // Add cleanup function to remove event listeners when element is deselected
        const cleanup = () => {
            isRotating = false;
            if (rotationRafId) {
                cancelAnimationFrame(rotationRafId);
                rotationRafId = null;
            }
            document.removeEventListener('mousemove', handleRotationMove);
            document.removeEventListener('mouseup', handleRotationEnd);
        };

        // Store cleanup function on the element
        const domElementForCleanup = document.querySelector(`[data-element-id="${id}"]`);
        if (domElementForCleanup) {
            domElementForCleanup._rotationCleanup = cleanup;
        }
    };

    // Add effect to handle deselection
    useEffect(() => {
        return () => {
            // Cleanup rotation handlers for all elements
            elements.forEach(el => {
                const domElement = document.querySelector(`[data-element-id="${el.id}"]`);
                if (domElement && domElement._rotationCleanup) {
                    domElement._rotationCleanup();
                    delete domElement._rotationCleanup;
                }
            });
        };
    }, [selectedIds]);

    const handleElementClick = (element, e) => {
        e.stopPropagation();
        // احذف شرط منع التحديد للصور
        // Get the design space container
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();
        
        // Get the element's position and dimensions
        const elementRect = e.currentTarget.getBoundingClientRect();
        const elementTop = elementRect.top - designSpaceRect.top;
        const elementRight = designSpaceRect.right - elementRect.right;
        const elementLeft = elementRect.left - designSpaceRect.left;
        const elementWidth = elementRect.width;
        const elementHeight = elementRect.height;
        
        // Define the threshold for edge detection (in pixels)
        const edgeThreshold = 50;
        
        // Define the distance between toolbar and element
        const toolbarDistance = 60;
        
        // Determine if element is near edges
        const isNearTop = elementTop < edgeThreshold;
        const isNearRight = elementRight < edgeThreshold;
        const isNearLeft = elementLeft < edgeThreshold;
        
        // Calculate toolbar position
        let position = {
            position: 'absolute',
            display: 'flex',
            zIndex: 1000,
            pointerEvents: 'auto'
        };
    
        // Calculate toolbar position based on element dimensions and position
        if (isNearRight) {
            // Position toolbar on the left side
            position = {
                ...position,
                left: `-${toolbarDistance}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else if (isNearLeft) {
            // Position toolbar on the right side with vertical alignment
            position = {
                ...position,
                left: `${elementWidth + 20}px`,
                top: `${elementHeight / 2}px`,
                transform: 'translateY(-50%)',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center'
            };
        } else {
            // Default position - above the element
            position = {
                ...position,
                top: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
    
        // If near top edge and not near sides, position below
        if (isNearTop && !isNearRight && !isNearLeft) {
            position = {
                ...position,
                top: 'auto',
                bottom: `-${toolbarDistance}px`,
                left: `${elementWidth / 2}px`,
                transform: 'translateX(-50%)',
                flexDirection: 'row',
                alignItems: 'center'
            };
        }
    
        // Update toolbar position state
        setToolbarPosition(position);
        
        // Update toolbar classes
        let classes = '';
        if (isNearTop) classes += ' top-edge';
        if (isNearRight) classes += ' right-edge';
        if (isNearLeft) classes += ' left-edge';
        setToolbarClasses(classes.trim());
        
        // Update selection
        setSelectedElement(element);
        if (e.ctrlKey || e.shiftKey || isMultiSelectActive) {
            setSelectedIds((prevSelectedIds) =>
                prevSelectedIds.includes(element.id)
                    ? prevSelectedIds.filter((selectedId) => selectedId !== element.id)
                    : [...prevSelectedIds, element.id]
            );
        } else {
            setSelectedIds([element.id]);
        }

        // فتح القائمة المناسبة في الشريط الجانبي بناءً على نوع العنصر
        // استثناء الصور (img) كما طلبت
        if (element.type !== 'img') {
            let targetTab = null;
            
            switch (element.type) {
                case 'text':
                case 'label':
                    targetTab = 'text';
                    break;
                case 'icon':
                    targetTab = 'icons';
                    break;
                case 'qr':
                    targetTab = 'qr';
                    break;
                case 'shape':
                case 'line':
                case 'frame':
                case 'sticker':
                case 'gradient':
                case 'svg':
                    targetTab = 'elements';
                    break;
                default:
                    // إذا لم يكن نوع معروف، لا تفتح أي قائمة
                    break;
            }
            
            // إرسال إشارة لفتح القائمة المناسبة في LeftSidebar
            if (targetTab) {
                // استخدام CustomEvent لإرسال إشارة لفتح القائمة
                const openTabEvent = new CustomEvent('openSidebarTab', {
                    detail: { tabId: targetTab }
                });
                document.dispatchEvent(openTabEvent);
            }
        } else {
            // فتح قائمة Image Editor للصور
            const openTabEvent = new CustomEvent('openSidebarTab', {
                detail: { tabId: 'image-editor' }
            });
            document.dispatchEvent(openTabEvent);
        }
    };

    // Function to get the position of resize handles
    const getResizeHandlePosition = (corner) => {
        switch (corner) {
            case "top-left":
                return { top: "-5px", left: "-5px" };
            case "top-right":
                return { top: "-5px", right: "-5px" };
            case "bottom-left":
                return { bottom: "-5px", left: "-5px" };
            case "bottom-right":
                return { bottom: "-5px", right: "-5px" };
            default:
                return {};
        }
    };

    const showAlignmentLines = (x, y, element, designSpaceRect) => {
        let vertical = null;
        let horizontal = null;

        // Tolerance for alignment
        const tolerance = 5;

        // Check alignment with container
        const containerCenterX = designSpaceRect.width / 2;
        const containerCenterY = designSpaceRect.height / 2;

        if (Math.abs(x + element.width / 2 - containerCenterX) < tolerance) {
            vertical = { position: containerCenterX, type: "solid" };
        }

        if (Math.abs(y + element.height / 2 - containerCenterY) < tolerance) {
            horizontal = { position: containerCenterY, type: "solid" };
        }

        // Check alignment with other elements
        elements.forEach((el) => {
            if (el.id !== element.id) {
                // Horizontal alignment checks
                if (Math.abs(el.y - y) < tolerance) {
                    horizontal = { position: el.y, type: "dotted" }; // Top alignment
                } else if (Math.abs(el.y + el.height - y) < tolerance) {
                    horizontal = { position: el.y + el.height, type: "dotted" }; // Bottom aligns with top
                } else if (Math.abs(el.y + el.height / 2 - (y + element.height / 2)) < tolerance) {
                    horizontal = { position: el.y + el.height / 2, type: "dotted" }; // Center horizontally
                }

                // Vertical alignment checks
                if (Math.abs(el.x - x) < tolerance) {
                    vertical = { position: el.x, type: "dotted" }; // Left alignment
                } else if (Math.abs(el.x + el.width - x) < tolerance) {
                    vertical = { position: el.x + el.width, type: "dotted" }; // Right aligns with left
                } else if (Math.abs(el.x + el.width / 2 - (x + element.width / 2)) < tolerance) {
                    vertical = { position: el.x + el.width / 2, type: "dotted" }; // Center vertically
                }
            }
        });

        setAlignmentLines({ vertical, horizontal });
    };

    // Prevent clicks inside the image sidebar or AI tools from deselecting the element
    const handleMainClick = (e) => {
        // Check if the click is inside the image sidebar or AI tools
        const isImageSidebarClick = e.target.closest('.image-edit-sidebar');
        const isAIToolsClick = e.target.closest('.ai-tools-tabs') || e.target.closest('.ai-tools-hub');
        const isTextAssistantClick = e.target.closest('.text-assistant') || e.target.closest('.p-button') || e.target.closest('.p-dropdown') || e.target.closest('.p-slider');
        const isAIToolsButton = e.target.closest('button') && (
            e.target.closest('button').textContent.includes('Apply Style') ||
            e.target.closest('button').textContent.includes('Enhance Image') ||
            e.target.closest('button').textContent.includes('Change Background') ||
            e.target.closest('button').textContent.includes('Apply to Selected') ||
            e.target.closest('button').textContent.includes('Add to Canvas') ||
            e.target.closest('button').textContent.includes('Reset')
        );

        // Don't deselect if clicking in sidebars, AI tools, text assistant, or specific buttons
        if (!isImageSidebarClick && !isAIToolsClick && !isTextAssistantClick && !isAIToolsButton) {
            setSelectedIds([]);
        }
    };

    useEffect(() => {
        const handleKeyDown = (e) => {
            // تجاهل إذا كان التركيز على input أو textarea أو contentEditable
            const active = document.activeElement;
            if (active && (active.tagName === 'INPUT' || active.tagName === 'TEXTAREA' || active.isContentEditable)) {
                return;
            }

            // نسخ العنصر
            if (e.ctrlKey && e.key.toLowerCase() === 'c') {
                if (selectedIds.length === 1) {
                    const el = elements.find(el => el.id === selectedIds[0]);
                    if (el) {
                        clipboardRef.current = JSON.parse(JSON.stringify(el));
                    }
                }
            }

            // لصق العنصر
            if (e.ctrlKey && e.key.toLowerCase() === 'v') {
                if (clipboardRef.current) {
                    const newElement = {
                        ...JSON.parse(JSON.stringify(clipboardRef.current)),
                        id: `el_${Date.now()}`,
                        x: (clipboardRef.current.x || 50) + 30,
                        y: (clipboardRef.current.y || 50) + 30
                    };
                    setElements(prev => [...prev, newElement]);
                    setSelectedIds([newElement.id]);
                }
            }

            // حذف العنصر
            if (e.key === 'Delete' || e.key === 'Backspace') {
                if (selectedIds.length > 0) {
                    setElements(prev => prev.filter(el => !selectedIds.includes(el.id)));
                    setSelectedIds([]);
                }
            }

            // تحريك العنصر بالأسهم
            const moveStep = e.shiftKey ? 20 : 5;
            if (["ArrowUp", "ArrowDown", "ArrowLeft", "ArrowRight"].includes(e.key) && selectedIds.length > 0) {
                e.preventDefault();
                setElements(prev => prev.map(el => {
                    if (!selectedIds.includes(el.id)) return el;
                    let { x, y } = el;
                    if (e.key === 'ArrowUp') y = Math.max(0, y - moveStep);
                    if (e.key === 'ArrowDown') y = y + moveStep;
                    if (e.key === 'ArrowLeft') x = Math.max(0, x - moveStep);
                    if (e.key === 'ArrowRight') x = x + moveStep;
                    return { ...el, x, y };
                }));
            }
        };
        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [elements, selectedIds]);

    // منطق الحفظ الفعلي بدون setSelectedIds([])
    const doSaveDesign = async () => {
        try {
            // تحويل النصوص إلى متغيرات في DOM
            const textElements = designSpaceRef.current.querySelectorAll('.user-data');
            textElements.forEach(element => {
                const value = element.textContent.trim();
                if (value) {
                    element.textContent = `{{${value}}}`;
                }
            });

            const content = designSpaceRef.current.innerHTML;

            // Get the current background from the DOM directly
            const designSpaceContent = document.getElementById('design-space-content');
            let actualBackground = '';
            let actualBackgroundStyle = null;

            if (designSpaceContent) {
                // Get the computed style
                const computedStyle = window.getComputedStyle(designSpaceContent);
                // Get background color and image
                const bgColor = computedStyle.backgroundColor;
                const bgImage = computedStyle.backgroundImage;
                // Handle solid color
                if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                    actualBackground = bgColor;
                }
                // Handle gradient or image
                else if (bgImage && bgImage !== 'none') {
                    actualBackground = bgImage;
                    actualBackgroundStyle = {
                        backgroundSize: computedStyle.backgroundSize,
                        backgroundPosition: computedStyle.backgroundPosition,
                        backgroundRepeat: computedStyle.backgroundRepeat
                    };
                }
            }

            // تحويل النصوص في init_template إلى متغيرات
            const initTemplateElements = elements.map(el => {
                if (el.type === 'text' && el.value) {
                    return {
                        ...el,
                        value: `{{${el.value}}}`
                    };
                }
                return el;
            });

            // Create template data with background information
            const templateData = {
                htmlTemplate: content,
                initTemplate: JSON.stringify(initTemplateElements),
                background: actualBackground || canvasBackgroundStyle?.backgroundColor,
                backgroundStyle: actualBackgroundStyle ? JSON.stringify(actualBackgroundStyle) : ''
            };

            // Update template data
            if (typeof updateTemplateData === 'function') {
                updateTemplateData(templateData);
            }

            // فتح نافذة الحفظ بعد التحديث
            if (typeof dialogHandler === 'function') {
                dialogHandler("createDesignTemplate");
            }

            // إعادة النصوص إلى حالتها الأصلية
            textElements.forEach(element => {
                const value = element.textContent.trim();
                if (value && value.startsWith('{{') && value.endsWith('}}')) {
                    element.textContent = value.slice(2, -2);
                }
            });
        } catch (error) {
            console.error('Error saving design:', error);
        }
    };

    // زر الحفظ: يلغي التحديد أولاً ثم يطلب الحفظ بعد إعادة التصيير
    const handleSaveClick = () => {
        setSelectedIds([]);
        setPendingSave(true);
    };

    // راقب pendingSave و selectedIds
    useEffect(() => {
        if (pendingSave && selectedIds.length === 0) {
            doSaveDesign();
            setPendingSave(false);
        }
    }, [pendingSave, selectedIds]);

    // Handle crop handle mouse down (سيتم إضافة المنطق لاحقاً)
    const handleCropHandleMouseDown = (e, id, side) => {
        e.stopPropagation();
        setActiveCrop({ elementId: id, side });
        // فقط للصور
        const elementIndex = elements.findIndex(el => el.id === id);
        if (elementIndex === -1) return;
        const element = { ...elements[elementIndex] };
        if (element.type !== 'img') return;

        // إعداد القيم الأولية
        const startX = e.clientX;
        const startY = e.clientY;
        const initialX = element.x;
        const initialY = element.y;
        const initialWidth = element.width;
        const initialHeight = element.height;
        const initialObjectPosX = element.objectPosX || 0; // نسبة مئوية (0-100)
        const initialObjectPosY = element.objectPosY || 0;

        // حدود التصميم (لمنع الخروج)
        const designSpace = designSpaceRef.current;
        const designSpaceRect = designSpace.getBoundingClientRect();

        let rafId = null;
        let lastClientX = startX;
        let lastClientY = startY;

        const updateCrop = () => {
            let newX = initialX;
            let newY = initialY;
            let newWidth = initialWidth;
            let newHeight = initialHeight;
            let objectPosX = initialObjectPosX;
            let objectPosY = initialObjectPosY;

            if (side === 'left') {
                const delta = lastClientX - startX;
                let maxDelta = initialWidth - 40; // لا تقلل أكثر من 40px
                let minDelta = -initialX; // لا تتجاوز حدود التصميم من اليسار
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newX = initialX + appliedDelta;
                newWidth = initialWidth - appliedDelta;
                objectPosX = ((initialObjectPosX * initialWidth) + appliedDelta) / newWidth;
            } else if (side === 'right') {
                const delta = startX - lastClientX;
                let maxDelta = initialWidth - 40;
                let minDelta = -(designSpaceRect.width - (initialX + initialWidth)); // لا تتجاوز حدود التصميم من اليمين
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newWidth = initialWidth - appliedDelta;
                objectPosX = (initialObjectPosX * initialWidth) / newWidth;
            } else if (side === 'top') {
                const delta = lastClientY - startY;
                let maxDelta = initialHeight - 40;
                let minDelta = -initialY;
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newY = initialY + appliedDelta;
                newHeight = initialHeight - appliedDelta;
                objectPosY = ((initialObjectPosY * initialHeight) + appliedDelta) / newHeight;
            } else if (side === 'bottom') {
                const delta = startY - lastClientY;
                let maxDelta = initialHeight - 40;
                let minDelta = -(designSpaceRect.height - (initialY + initialHeight));
                let appliedDelta = Math.max(Math.min(delta, maxDelta), minDelta);
                newHeight = initialHeight - appliedDelta;
                objectPosY = (initialObjectPosY * initialHeight) / newHeight;
            }

            // عدل العنصر في المصفوفة
            const newElements = [...elements];
            newElements[elementIndex] = {
                ...element,
                x: newX,
                y: newY,
                width: newWidth,
                height: newHeight,
                objectPosX: objectPosX,
                objectPosY: objectPosY
            };
            setElements(newElements);
            rafId = null;
        };

        const handleMouseMove = (ev) => {
            lastClientX = ev.clientX;
            lastClientY = ev.clientY;
            if (!rafId) rafId = requestAnimationFrame(updateCrop);
        };
        const handleMouseUp = () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
            setActiveCrop({ elementId: null, side: null });
        };
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
    };

    // عند تحميل التصميم أو تغييره
    useEffect(() => {
        if (design?.elements) {
            setInitialElements(JSON.parse(JSON.stringify(design.elements)));
        } else if (elements.length > 0) {
            setInitialElements(JSON.parse(JSON.stringify(elements)));
        }
        if (canvasBackgroundStyle) {
            setInitialBackgroundStyle(JSON.parse(JSON.stringify(canvasBackgroundStyle)));
        }
    }, [design, cardType]);

    // مقارنة التعديلات
    useEffect(() => {
        const elementsEqual = JSON.stringify(elements) === JSON.stringify(initialElements);
        const bgEqual = JSON.stringify(canvasBackgroundStyle) === JSON.stringify(initialBackgroundStyle);
        setIsDirty(!(elementsEqual && bgEqual));
    }, [elements, canvasBackgroundStyle, initialElements, initialBackgroundStyle]);

    // بعد الحفظ، أعد تعيين الأصل
    useEffect(() => {
        if (!isDirty && elements.length > 0) {
            setInitialElements(JSON.parse(JSON.stringify(elements)));
            setInitialBackgroundStyle(JSON.parse(JSON.stringify(canvasBackgroundStyle)));
        }
    }, [isDirty]);

    return (
        <div className={`flex ${isMobile ? 'flex-col' : 'flex-col'} relative w-full items-start h-full`} onClick={handleMainClick}> {/*should probably refractor it*/}
            {/* Top Toolbar - Dark Background - Hide on mobile */}
            {!isMobile && (
                <div className="w-full bg-gray-800 text-white p-2">
                    {/* Placeholder for top toolbar */}
                </div>
            )}

            {/* Canva Toolbar with Save Button - Hide on mobile */}
            {!isMobile && (
                <CanvaToolbar
                    updateTemplateData={updateTemplateData}
                    saveDesignHandler={handleSaveClick}
                    isDirty={isDirty}
                />
            )}

            {/* Second Toolbar */}
            <div className={`w-full flex items-center p-2 bg-gray-50 border-b border-gray-200 ${isMobile ? 'flex-nowrap gap-2' : ''}`}>
                {/* Left: Back Button */}
                <div className="flex items-center flex-shrink-0">
                    <motion.button
                        className="flex items-center px-3 py-1.5 rounded-md bg-gradient-to-r from-gray-800 to-gray-700 text-white shadow-sm"
                        style={{ minWidth: 70 }}
                        onClick={() => window.history.back()}
                        whileHover={{
                            scale: 1.05,
                            boxShadow: "0 4px 8px rgba(0,0,0,0.2)"
                        }}
                        whileTap={{ scale: 0.95 }}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3 }}
                    >
                        <motion.span
                            animate={{ x: [0, -3, 0] }}
                            transition={{ duration: 1.5, repeat: Infinity, repeatType: "loop" }}
                        >
                            <FiChevronLeft size={18} />
                        </motion.span>
                        <span className="ml-1 font-medium">Back</span>
                    </motion.button>
                </div>

                {/* Center: Card Type Dropdown (mobile only) */}
                {isMobile && (
                    <div className="flex-1 flex justify-center">
                        <TypeControl hideLabel={true} />
                    </div>
                )}

                {/* Right: Other Controls (includes TypeControl on web) */}
                <div className="flex items-center flex-shrink-0 ml-auto">
                    {!isMobile && <TypeControl />}
                    {!isMobile && <AlignmentControl />}
                    {!isMobile && <ResizeInputs />}
                    {!isMobile && <DuplicateControl />}
                    {!isMobile && <DeleteControl />}
                    <button
                        className="ml-2 p-2 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white shadow-lg hover:from-blue-600 hover:to-cyan-600 flex items-center justify-center border-2 border-white/70"
                        style={{ boxShadow: '0 4px 16px 0 rgba(0, 180, 255, 0.15)' }}
                        onClick={() => document.querySelector('.help-guide-button').click()}
                        title="Help"
                    >
                        <FiHelpCircle size={22} className="drop-shadow" />
                    </button>
                </div>
            </div>

            {/* Main Content Area - Responsive layout */}
            <div className={`flex ${isMobile ? 'flex-col' : 'flex-row'} w-full h-full overflow-hidden`}>
                {/* Left Sidebar - Desktop only */}
                {!isMobile && <LeftSidebar isMobile={isMobile} />}

                {/* Canvas Area */}
                <div className={`flex-grow flex flex-col h-full ${isMobile ? 'pb-24' : ''}`}>
                    {cardType && (
                        <div className={`flex-grow flex justify-center items-center ${isMobile ? 'p-2' : 'p-4'} overflow-auto relative design-space-container`}>
                            <DesignSpaceBackground />
                            <div
                                className={`design-space relative shadow-xl ${isMobile ? 'mobile-canvas' : ''} ${zoomLevel !== 100 ? 'no-interactive-zoom' : ''}`}
                                ref={designSpaceRef}
                                style={{
                                    transformOrigin: 'center center',
                                    transition: 'transform 0.2s ease',
                                    ...(isMobile && {
                                        transform: 'scale(0.8)',
                                        maxWidth: 'calc(100vw - 16px)',
                                        maxHeight: 'calc(100vh - 200px)'
                                    })
                                }}
                            >
                                <div
                                    id="design-space-content"
                                    onContextMenu={(e) => e.preventDefault()}
                                    style={{
                                        width: `${cardType?.width}px`,
                                        height: `${cardType?.height}px`,
                                        position: "relative",
                                        backgroundColor: "transparent",
                                        backgroundImage: 'none',
                                        boxShadow: '0 0 40px rgba(0, 0, 0, 0.25)',
                                        backgroundSize: canvasBackgroundStyle?.backgroundSize,
                                        backgroundBlendMode: 'normal',
                                        backgroundPosition: canvasBackgroundStyle?.backgroundPosition || 'center',
                                        backgroundRepeat: canvasBackgroundStyle?.backgroundRepeat || 'repeat',
                                        opacity: 1
                                    }}
                                >
                                    {/* Professional corner marks */}
                                    <div className="corner-mark top-left"></div>
                                    <div className="corner-mark top-right"></div>
                                    <div className="corner-mark bottom-left"></div>
                                    <div className="corner-mark bottom-right"></div>
                                    {elements?.map((el) => (
                                        <div
                                            key={el.id}
                                            data-element-id={el.id}
                                            style={{
                                                position: "absolute",
                                                top: el.y,
                                                left: el.x,
                                                width: el.width,
                                                height: el.height,
                                                cursor: "move",
                                                zIndex: el.zIndex || 0,
                                                transform: el.rotation ? `rotate(${el.rotation}deg)` : undefined,
                                                transformOrigin: 'center center'
                                            }}
                                            className={`draggable-element ${selectedIds.includes(el.id) ? 'selected' : ''}`}
                                            onMouseDown={(e) => handleMouseDown(e, el.id)}
                                            onClick={(e) => handleElementClick(el, e)}
                                        >
                                            {/* Apply image-specific styles only to the content, not the toolbar */}
                                            <div 
                                                style={{
                                                    width: '100%',
                                                    height: '100%',
                                                    ...(el.style || {}) // Apply any custom styles like filters only to content
                                                }}
                                            >
                                                <Element el={el} userData={design?.userData || {}} selectedIds={selectedIds} />
                                            </div>

                                            {/* Crop handles for images only, at mid-sides */}
                                            {selectedIds.includes(el.id) && el.type === 'img' && (
                                                <>
                                                    {/* Top (منتصف الأعلى) */}
                                                    <div
                                                        className="crop-handle crop-handle-top"
                                                        style={{
                                                            position: 'absolute',
                                                            top: '-7px',
                                                            left: '50%',
                                                            transform: 'translateX(-50%)',
                                                            cursor: 'ns-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'top')}
                                                    />
                                                    {/* Bottom (منتصف الأسفل) */}
                                                    <div
                                                        className="crop-handle crop-handle-bottom"
                                                        style={{
                                                            position: 'absolute',
                                                            bottom: '-7px',
                                                            left: '50%',
                                                            transform: 'translateX(-50%)',
                                                            cursor: 'ns-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'bottom')}
                                                    />
                                                    {/* Left (منتصف اليسار) */}
                                                    <div
                                                        className="crop-handle crop-handle-left"
                                                        style={{
                                                            position: 'absolute',
                                                            left: '-7px',
                                                            top: '50%',
                                                            transform: 'translateY(-50%)',
                                                            cursor: 'ew-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'left')}
                                                    />
                                                    {/* Right (منتصف اليمين) */}
                                                    <div
                                                        className="crop-handle crop-handle-right"
                                                        style={{
                                                            position: 'absolute',
                                                            right: '-7px',
                                                            top: '50%',
                                                            transform: 'translateY(-50%)',
                                                            cursor: 'ew-resize',
                                                        }}
                                                        onMouseDown={(e) => handleCropHandleMouseDown(e, el.id, 'right')}
                                                    />
                                                </>
                                            )}

                                            {selectedIds.includes(el.id) && draggingElementId !== el.id && !isActuallyDragging.current && (
                                                <>
                                                    {/* Element Controls */}
                                                    <div className={`element-controls element-controls-sm ${toolbarClasses}`} style={toolbarPosition}>
                                                        {/* زر أيقونة لتغيير اللون بنفس شكل ColorPicker */}
                                                        <button
                                                            className="element-control-btn color-picker-btn"
                                                            title="change color"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                // إرسال حدث إغلاق القوائم
                                                                const closeEvent = new CustomEvent('closeSidebarTabs');
                                                                document.dispatchEvent(closeEvent);
                                                                setTimeout(() => {
                                                                    setColorPickerTargetId(el.id);
                                                                }, 100);
                                                            }}
                                                        >
                                                            <MdOutlineColorLens size={18} />
                                                        </button>
                                                        <button
                                                            className="element-control-btn rotate-btn"
                                                            title="Rotate 90°"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                const currentRotation = el.rotation || 0;
                                                                updateElement(el.id, { rotation: currentRotation + 90 });
                                                            }}
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                                                                <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                                                            </svg>
                                                        </button>
                                                        <button
                                                            className="element-control-btn forward-btn"
                                                            title="Bring Forward"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                bringToFront(el.id);
                                                            }}
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M8 6.5a.5.5 0 0 1 .5.5v1.5H10a.5.5 0 0 1 0 1H8.5V11a.5.5 0 0 1-1 0V9.5H6a.5.5 0 0 1 0-1h1.5V7a.5.5 0 0 1 .5-.5z"/>
                                                                <path d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-7 7 7 7 0 0 0 7 7 7 7 0 0 0 7-7 7 7 0 0 0-7-7z"/>
                                                            </svg>
                                                        </button>
                                                        <button
                                                            className="element-control-btn backward-btn"
                                                            title="Send Backward"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                sendToBack(el.id);
                                                            }}
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                                                                <path d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/>
                                                            </svg>
                                                        </button>
                                                        <button
                                                            className="element-control-btn duplicate-btn"
                                                            title="Duplicate"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                const newElement = {
                                                                    ...JSON.parse(JSON.stringify(el)),
                                                                    id: `${el.id}-copy-${Date.now()}`,
                                                                    x: el.x + 20,
                                                                    y: el.y + 20
                                                                };
                                                                const updatedElements = [...elements, newElement];
                                                                setElements(updatedElements);
                                                                setSelectedIds([newElement.id]);
                                                            }}
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"/>
                                                                <path d="M9.5 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"/>
                                                            </svg>
                                                        </button>
                                                        <button
                                                            className="element-control-btn delete-btn"
                                                            title="Delete"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                const updatedElements = elements.filter(elem => elem.id !== el.id);
                                                                setElements(updatedElements);
                                                                setSelectedIds([]);
                                                                const elementToDelete = document.querySelector(`[data-element-id="${el.id}"]`);
                                                                if (elementToDelete) {
                                                                    elementToDelete.style.transition = 'all 0.2s ease';
                                                                    elementToDelete.style.transform = 'scale(0.8)';
                                                                    elementToDelete.style.opacity = '0';
                                                                }
                                                            }}
                                                        >
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                                                <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                                                                <path fillRule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                                                            </svg>
                                                        </button>
                                                        {/* زر حفظ التصميم النصي */}
                                                        {(el.type === 'text' || el.type === 'label') && (
                                                            <button
                                                                className="element-control-btn save-style-btn"
                                                                title="Save as Text Style"
                                                                onClick={(e) => {
                                                                    e.stopPropagation();
                                                                    // جمع خصائص التصميم
                                                                    const styleObj = {
                                                                        fontFamily: el.fontFamily,
                                                                        fontSize: el.fontSize,
                                                                        fontWeight: el.fontWeight,
                                                                        color: el.color,
                                                                        backgroundColor: el.backgroundColor,
                                                                        textAlign: el.textAlign,
                                                                        lineHeight: el.lineHeight,
                                                                        letterSpacing: el.letterSpacing,
                                                                        textDecoration: el.textDecoration,
                                                                        textTransform: el.textTransform,
                                                                        fontStyle: el.fontStyle,
                                                                        opacity: el.opacity,
                                                                        textShadow: el.textShadow,
                                                                        WebkitTextStroke: el.WebkitTextStroke,
                                                                        transform: el.transform,
                                                                        textEffect: el.textEffect,
                                                                        textShadowColor: el.textShadowColor,
                                                                        textShadowBlur: el.textShadowBlur,
                                                                        textShadowOffset: el.textShadowOffset
                                                                    };
                                                                    saveTextStyle(styleObj);
                                                                }}
                                                            >
                                                                <FaRegStar size={18} />
                                                            </button>
                                                        )}
                                                    </div>

                                                    {/* Layer indicator */}
                                                    <div className="layer-indicator">
                                                        Layer {el.zIndex || 0}
                                                    </div>

                                                    {/* Rotation Handle */}
                                                    <div
                                                        className="rotation-handle"
                                                        onMouseDown={(e) => handleRotationStart(e, el.id)}
                                                    />

                                                    {/* Resize/Rotate Handles */}
                                                    {["top-left", "top-right", "bottom-left", "bottom-right"].map((corner) => (
                                                        <div
                                                            key={corner}
                                                            className={`resize-handle`}
                                                            style={{
                                                                cursor: ["top-left", "bottom-right"].includes(corner) ? "nwse-resize" : "nesw-resize",
                                                                ...getResizeHandlePosition(corner),
                                                            }}
                                                            onMouseDown={(e) => {
                                                                e.preventDefault();
                                                                setActiveResize({ elementId: el.id, corner });
                                                                handleMouseDown(e, el.id, corner);
                                                            }}
                                                            onContextMenu={(e) => e.preventDefault()}
                                                            title="Drag to resize"
                                                        />
                                                    ))}
                                                </>
                                            )}
                                        </div>
                                    ))}
                                    <AlignmentContainer alignmentLines={alignmentLines} />
                                    {/* Crop Size Indicator أثناء السحب - داخل الكانفاس */}
                                    {(activeCrop.elementId || activeResize.elementId) && (() => {
                                        const el = elements.find(e => e.id === (activeCrop.elementId || activeResize.elementId));
                                        if (!el) return null;
                                        const indicatorWidth = 80;
                                        let left = el.x + (el.width / 2) - (indicatorWidth / 2);
                                        let top = el.y + el.height + 22;
                                        if (left + indicatorWidth > cardType?.width) {
                                            left = cardType?.width - indicatorWidth - 8;
                                        }
                                        if (left < 8) {
                                            left = 8;
                                        }
                                        if (top + 28 > cardType?.height) {
                                            top = cardType?.height - 28;
                                        }
                                        return (
                                            <div
                                                style={{
                                                    position: 'absolute',
                                                    left: left,
                                                    top: top,
                                                    background: 'rgba(0,0,0,0.85)',
                                                    color: '#fff',
                                                    borderRadius: 6,
                                                    padding: '2px 10px',
                                                    fontSize: 11,
                                                    fontWeight: 500,
                                                    boxShadow: '0 1px 4px rgba(0,0,0,0.13)',
                                                    zIndex: 2000,
                                                    pointerEvents: 'none',
                                                    userSelect: 'none',
                                                    minWidth: indicatorWidth,
                                                    textAlign: 'center',
                                                }}
                                            >
                                                W: {Math.round(el.width)} px | H: {Math.round(el.height)} px
                                            </div>
                                        );
                                    })()}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* ColorPicker العائم بجانب منطقة التصميم مباشرة */}
            {colorPickerTargetId && (
                <div 
                    className="color-picker-container"
                    style={{
                        position: 'fixed',
                        top: '50%',
                        left: 'calc(50% + 400px)', // بجانب منطقة التصميم مباشرة
                        transform: 'translateY(-50%)',
                        zIndex: 9999,
                        transition: 'all 0.3s ease',
                        pointerEvents: 'auto'
                    }}
                >
                    <ColorPicker
                        elementId={colorPickerTargetId}
                        open={true}
                        onClose={() => setColorPickerTargetId(null)}
                    />
                </div>
            )}
        </div>
    );
};

DesignSpace.propTypes = {
    updateTemplateData: PropTypes.func.isRequired,
    design: PropTypes.object.isRequired
};

export default DesignSpace;